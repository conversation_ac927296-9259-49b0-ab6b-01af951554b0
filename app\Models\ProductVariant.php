<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;
use Illuminate\Support\Facades\Storage;
use App\Models\Product;
use App\Models\Color;
use App\Models\Size;

class ProductVariant extends Model implements HasMedia
{
    use HasFactory, InteractsWithMedia;

    protected $fillable = [
        'product_id',
        'color_id',
        'size_id',
        'sku',
        'price_adjustment',
        'stock_quantity',
        'is_active',
    ];

    protected $casts = [
        'price_adjustment' => 'decimal:2',
        'is_active' => 'boolean',
    ];

    /**
     * Get the product that owns the variant.
     */
    public function product()
    {
        return $this->belongsTo(Product::class);
    }

    /**
     * Get the color of the variant.
     */
    public function color()
    {
        return $this->belongsTo(Color::class);
    }

    /**
     * Get the size of the variant.
     */
    public function size()
    {
        return $this->belongsTo(Size::class);
    }

    /**
     * Accessor for the final price of the variant.
     */
    public function getPriceAttribute()
    {
        if ($this->product && isset($this->product->price)) {
            return $this->product->price + ($this->price_adjustment ?? 0);
        }
        return $this->price_adjustment ?? 0; // Or handle as an error/default
    }

    /**
     * Accessor for the variant's image URL.
     * Falls back to product's main image if variant image is not set.
     */
    public function registerMediaCollections(): void
    {
        $this->addMediaCollection('variant_image')
            ->singleFile();
    }

    public function getImageUrlAttribute()
    {
        if ($this->hasMedia('variant_image')) {
            return $this->getFirstMediaUrl('variant_image');
        }

        // Fallback to product's main image
        return $this->product->image_url;
    }

    /**
     * Get the variant's display name (e.g., "Red - Large")
     */
    public function getDisplayNameAttribute()
    {
        $parts = [];

        if ($this->color) {
            $parts[] = $this->color->name;
        }

        if ($this->size) {
            $parts[] = $this->size->name;
        }

        return implode(' - ', $parts) ?: 'Default';
    }

    /**
     * Get the variant's full name including product name
     */
    public function getFullNameAttribute()
    {
        return $this->product->name . ' (' . $this->display_name . ')';
    }

    /**
     * Check if variant is in stock
     */
    public function isInStock()
    {
        return $this->is_active && $this->stock_quantity > 0;
    }

    /**
     * Check if variant is available for purchase (active and in stock)
     */
    public function isAvailable()
    {
        try {
            $productIsActive = $this->product ? $this->product->is_active : true;
            $isAvailable = $this->is_active && $productIsActive && $this->stock_quantity > 0;

            \Log::debug('ProductVariant Availability Check', [
                'variant_id' => $this->id,
                'product_id' => $this->product_id,
                'variant_is_active' => $this->is_active,
                'product_is_active' => $productIsActive,
                'stock_quantity' => $this->stock_quantity,
                'is_available' => $isAvailable
            ]);

            return $isAvailable;
        } catch (\Exception $e) {
            \Log::error('Error checking variant availability', [
                'variant_id' => $this->id,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Get formatted price
     */
    public function getFormattedPriceAttribute()
    {
        return '₦' . number_format($this->price, 2);
    }

    /**
     * Scope for active variants
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope for in-stock variants
     */
    public function scopeInStock($query)
    {
        return $query->where('stock_quantity', '>', 0);
    }

    /**
     * Scope for available variants (active and in stock)
     */
    public function scopeAvailable($query)
    {
        return $query->active()->inStock();
    }
}
