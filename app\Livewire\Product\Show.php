<?php

namespace App\Livewire\Product;

use App\Models\Product;
use App\Models\ProductVariant;
use Livewire\Component;
use Livewire\Attributes\Layout;
use Illuminate\Support\Facades\Auth;

#[Layout('layouts.app')]
class Show extends Component
{
    public Product $product;
    public $selectedColorId = null;
    public $selectedSizeId = null;
    public $selectedVariantId = null;
    public $selectedVariant = null;
    public $quantity = 1;
    public $availableColors;
    public $availableSizes;
    public $inWishlist = false;
    public $loadingCart = false;
    public $loadingWishlist = false;

    public function mount(Product $product)
    {
        // Load the product with all necessary relationships
        $this->product = $product->load([
            'vendor.user',
            'category',
            'brand',
            'variants.color',
            'variants.size',
            'reviews.user',
            'specifications'
        ]);

        // Initialize available colors and sizes - ensure they are always collections
        $this->availableColors = $this->product->available_colors ?? collect();
        $this->availableSizes = $this->product->available_sizes ?? collect();

        // Check if product is in wishlist
        $this->inWishlist = $this->checkWishlist();
    }

    public function selectColor($colorId)
    {
        $this->selectedColorId = $colorId;
        $this->updateSelectedVariant();
    }

    public function selectSize($sizeId)
    {
        $this->selectedSizeId = $sizeId;
        $this->updateSelectedVariant();
    }

    private function updateSelectedVariant()
    {
        if ($this->selectedColorId || $this->selectedSizeId) {
            $this->selectedVariant = $this->product->getVariantByColorAndSize(
                $this->selectedColorId,
                $this->selectedSizeId
            );
            $this->selectedVariantId = $this->selectedVariant?->id;
        } else {
            $this->selectedVariant = null;
            $this->selectedVariantId = null;
        }
    }

    public function incrementQuantity()
    {
        $this->quantity++;
    }

    public function decrementQuantity()
    {
        if ($this->quantity > 1) {
            $this->quantity--;
        }
    }

    public function addToCart()
    {
        $this->loadingCart = true;

        try {
            // Check if product has variants and if variant is required
            $hasVariants = $this->availableColors->isNotEmpty() || $this->availableSizes->isNotEmpty();

            if ($hasVariants && !$this->selectedVariantId) {
                $this->dispatch('toast', message: 'Please select product options before adding to cart.', type: 'error');
                $this->loadingCart = false;
                return;
            }

            // Get the item to add (variant or product)
            $itemToAdd = $this->selectedVariant ?? $this->product;

            // Check stock availability
            $stockLevel = $this->selectedVariant ? $this->selectedVariant->stock_quantity : $this->product->stock;
            if ($stockLevel < $this->quantity) {
                $this->dispatch('toast', message: "Sorry, only {$stockLevel} units are available.", type: 'error');
                $this->loadingCart = false;
                return;
            }

            // Add to cart
            $cart = session()->get('cart', []);
            $cartKey = $this->selectedVariant ? "product_{$this->product->id}_variant_{$this->selectedVariant->id}" : "product_{$this->product->id}";

            $price = $this->selectedVariant ? $this->selectedVariant->price : $this->product->getCurrentPrice();

            if (isset($cart[$cartKey])) {
                $cart[$cartKey]['quantity'] += $this->quantity;
            } else {
                $cart[$cartKey] = [
                    'product_id' => $this->product->id,
                    'variant_id' => $this->selectedVariant?->id,
                    'name' => $this->product->name,
                    'variant_name' => $this->selectedVariant?->display_name,
                    'price' => $price,
                    'quantity' => $this->quantity,
                    'image' => $this->selectedVariant?->image_url ?? $this->product->image_url,
                    'vendor_id' => $this->product->vendor_id,
                    'sku' => $this->selectedVariant?->sku ?? $this->product->sku,
                ];
            }

            session()->put('cart', $cart);

            // Dispatch events
            $this->dispatch('cartUpdated');
            $this->dispatch('cart-updated-total', count($cart));
            $this->dispatch('toast', message: 'Product added to cart successfully!', type: 'success');

        } catch (\Exception $e) {
            \Log::error('Error adding product to cart', [
                'product_id' => $this->product->id,
                'variant_id' => $this->selectedVariantId,
                'error' => $e->getMessage()
            ]);
            $this->dispatch('toast', message: 'Error adding product to cart. Please try again.', type: 'error');
        }

        $this->loadingCart = false;
    }

    public function toggleWishlist()
    {
        if (!Auth::check()) {
            return redirect()->route('login');
        }

        $this->loadingWishlist = true;

        try {
            $user = Auth::user();
            $wishlistItem = $user->wishlist()->where('product_id', $this->product->id)->first();

            if ($wishlistItem) {
                $wishlistItem->delete();
                $this->inWishlist = false;
                $this->dispatch('toast', message: 'Product removed from wishlist.', type: 'success');
            } else {
                $user->wishlist()->create(['product_id' => $this->product->id]);
                $this->inWishlist = true;
                $this->dispatch('toast', message: 'Product added to wishlist.', type: 'success');
            }
        } catch (\Exception $e) {
            \Log::error('Error toggling wishlist', [
                'product_id' => $this->product->id,
                'user_id' => Auth::id(),
                'error' => $e->getMessage()
            ]);
            $this->dispatch('toast', message: 'Error updating wishlist. Please try again.', type: 'error');
        }

        $this->loadingWishlist = false;
    }

    private function checkWishlist()
    {
        if (!Auth::check()) {
            return false;
        }

        return Auth::user()->wishlist()->where('product_id', $this->product->id)->exists();
    }

    public function render()
    {
        return view('livewire.product.show');
    }
}
