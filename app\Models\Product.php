<?php
namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\Sluggable\HasSlug;
use Spatie\Sluggable\SlugOptions;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;
use Spatie\MediaLibrary\MediaCollections\Models\Media;
use Illuminate\Support\Facades\Storage;

class Product extends Model implements HasMedia
{
    use HasFactory, HasSlug, SoftDeletes, InteractsWithMedia;

    protected $fillable = [
        'vendor_id',
        'category_id',
        'brand_id',
        'name',
        'slug',
        'description',
        'short_description',
        'price',
        'discount_price',
        'stock', // PRIMARY inventory field - use this consistently
        // 'quantity' - DEPRECATED: Use 'stock' instead for consistency
        'sku',
        'image_url',
        'weight',
        'height',
        'width',
        'length',
        'is_active',
        // Note: is_featured and is_best_seller removed from fillable for security
        // These should only be set by admins through specific methods
    ];

    /**
     * Attributes that should not be mass assignable for security
     */
    protected $guarded = [
        'id',
        'is_featured',
        'is_best_seller',
        'created_at',
        'updated_at',
        'deleted_at',
    ];

    protected $casts = [
        'price' => 'decimal:2',
        'discount_price' => 'decimal:2',
        'weight' => 'decimal:2',
        'height' => 'decimal:2',
        'width' => 'decimal:2',
        'length' => 'decimal:2',
        'is_active' => 'boolean',
        'is_featured' => 'boolean',
    ];

    /**
     * Get the options for generating the slug.
     */
    public function getSlugOptions(): SlugOptions
    {
        return SlugOptions::create()
            ->generateSlugsFrom('name')
            ->saveSlugsTo('slug')
            ->doNotGenerateSlugsOnUpdate();
    }

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($product) {
            \Log::info('Product Creating', [
                'name' => $product->name,
                'vendor_id' => $product->vendor_id,
                'category_id' => $product->category_id,
                'price' => $product->price,
                'stock' => $product->stock,
                'timestamp' => now()->toISOString()
            ]);
        });

        static::created(function ($product) {
            \Log::info('Product Created Successfully', [
                'product_id' => $product->id,
                'name' => $product->name,
                'slug' => $product->slug,
                'vendor_id' => $product->vendor_id,
                'category_id' => $product->category_id,
                'price' => $product->price,
                'stock' => $product->stock
            ]);
        });

        static::updating(function ($product) {
            \Log::info('Product Updating', [
                'product_id' => $product->id,
                'name' => $product->name,
                'old_stock' => $product->getOriginal('stock'),
                'new_stock' => $product->stock,
                'old_price' => $product->getOriginal('price'),
                'new_price' => $product->price,
                'old_is_active' => $product->getOriginal('is_active'),
                'new_is_active' => $product->is_active
            ]);
        });

        static::updated(function ($product) {
            \Log::info('Product Updated Successfully', [
                'product_id' => $product->id,
                'name' => $product->name,
                'stock' => $product->stock,
                'price' => $product->price,
                'is_active' => $product->is_active
            ]);
        });
    }

    public function vendor()
    {
        return $this->belongsTo(Vendor::class);
    }

    public function category()
    {
        return $this->belongsTo(Category::class);
    }

    public function brand()
    {
        return $this->belongsTo(Brand::class);
    }

    public function orderItems()
    {
        return $this->hasMany(OrderItem::class);
    }

    public function reviews()
    {
        return $this->hasMany(Review::class);
    }
    
    public function wishlists()
    {
        return $this->hasMany(Wishlist::class);
    }

    public function wishlistedBy()
    {
        return $this->belongsToMany(User::class, 'wishlists');
    }

    public function specifications()
    {
        return $this->hasMany(ProductSpecification::class);
    }

    public function activeVariants()
    {
        return $this->hasMany(ProductVariant::class)->where('is_active', true);
    }

    public function availableVariants()
    {
        return $this->hasMany(ProductVariant::class)->available();
    }

    public function getCurrentPrice()
    {
        return $this->discount_price > 0 ? $this->discount_price : $this->price;
    }
    
    public function getDiscountPercentage()
    {
        if (!$this->discount_price || $this->discount_price <= 0 || $this->price <= 0) {
            return 0;
        }
        
        return round((($this->price - $this->discount_price) / $this->price) * 100);
    }
    
    public function getRouteKeyName()
    {
        return 'slug';
    }
    
    /**
     * Check if product is on sale
     *
     * @return bool
     */
    public function isOnSale()
    {
        return $this->discount_price > 0 && $this->discount_price < $this->price;
    }

    public function averageRating()
    {
        return $this->reviews()->avg('rating');
    }

    public function reviewCount()
    {
        return $this->reviews()->count();
    }

    /**
     * Get the product variants for the product.
     */
    public function variants()
    {
        return $this->hasMany(ProductVariant::class);
    }

    /**
     * Accessor to check if the product has variants.
     */
    public function getHasVariantsAttribute(): bool
    {
        return $this->variants()->exists();
    }

    /**
     * Get available colors for this product (only in-stock variants)
     */
    public function getAvailableColorsAttribute()
    {
        try {
            return $this->variants()
                ->with('color')
                ->whereNotNull('color_id')
                ->where('is_active', true)
                ->where('stock_quantity', '>', 0)
                ->get()
                ->pluck('color')
                ->unique('id')
                ->filter()
                ->values();
        } catch (\Exception $e) {
            \Log::error('Error getting available colors for product', [
                'product_id' => $this->id,
                'error' => $e->getMessage()
            ]);
            return collect();
        }
    }

    /**
     * Get available sizes for this product (only in-stock variants)
     */
    public function getAvailableSizesAttribute()
    {
        try {
            return $this->variants()
                ->with('size')
                ->whereNotNull('size_id')
                ->where('is_active', true)
                ->where('stock_quantity', '>', 0)
                ->get()
                ->pluck('size')
                ->unique('id')
                ->filter()
                ->values();
        } catch (\Exception $e) {
            \Log::error('Error getting available sizes for product', [
                'product_id' => $this->id,
                'error' => $e->getMessage()
            ]);
            return collect();
        }
    }

    /**
     * Get total stock across all variants
     */
    public function getTotalVariantStockAttribute()
    {
        return $this->variants()->sum('stock_quantity');
    }

    /**
     * Get the cheapest variant price
     */
    public function getCheapestVariantPriceAttribute()
    {
        $cheapestVariant = $this->variants()
            ->where('is_active', true)
            ->orderByRaw('(CASE WHEN price_adjustment IS NULL THEN ? ELSE ? + price_adjustment END)', [$this->price, $this->price])
            ->first();

        return $cheapestVariant ? $cheapestVariant->price : $this->getCurrentPrice();
    }

    /**
     * Get variant by color and size
     */
    public function getVariantByColorAndSize($colorId = null, $sizeId = null)
    {
        return $this->variants()
            ->where('color_id', $colorId)
            ->where('size_id', $sizeId)
            ->where('is_active', true)
            ->first();
    }

    /**
     * Register media collections for the product.
     */
    public function registerMediaCollections(): void
    {
        $this->addMediaCollection('product_images')
            ->acceptsMimeTypes(['image/jpeg', 'image/png', 'image/webp', 'image/jpg'])
            ->singleFile();

        $this->addMediaCollection('product_gallery')
            ->acceptsMimeTypes(['image/jpeg', 'image/png', 'image/webp', 'image/jpg']);
    }

    /**
     * Register media conversions for the product.
     */
    public function registerMediaConversions(Media $media = null): void
    {
        $this->addMediaConversion('thumb')
            ->width(300)
            ->height(300)
            ->sharpen(10)
            ->optimize()
            ->performOnCollections('product_images', 'product_gallery');

        $this->addMediaConversion('large')
            ->width(800)
            ->height(800)
            ->sharpen(10)
            ->optimize()
            ->performOnCollections('product_images', 'product_gallery');
    }

    /**
     * Get the product's main image URL.
     *
     * @return string
     */
    public function getImageUrlAttribute(): string
    {
        \Log::debug('Product Image URL Request', [
            'product_id' => $this->id,
            'product_name' => $this->name,
            'has_media' => $this->hasMedia('product_images'),
            'database_image_url' => $this->attributes['image_url'] ?? null
        ]);

        try {
            // First try to get from media library
            $media = $this->getFirstMedia('product_images');
            if ($media) {
                $url = $media->getUrl();
                \Log::debug('Product Image from Media Library', [
                    'product_id' => $this->id,
                    'media_id' => $media->id,
                    'media_url' => $url
                ]);
                // Validate URL
                if (filter_var($url, FILTER_VALIDATE_URL) || file_exists(public_path(str_replace(url('/'), '', $url)))) {
                    return $url;
                }
            }
        } catch (\Exception $e) {
            \Log::warning('Product media library error', [
                'product_id' => $this->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }

        // Fallback to database image_url if exists and file exists
        if (!empty($this->attributes['image_url'])) {
            $imageUrl = $this->attributes['image_url'];
            
            // If it's a full URL, validate it
            if (filter_var($imageUrl, FILTER_VALIDATE_URL)) {
                return $imageUrl;
            }
            
            // If it's a relative path, check if file exists
            $fullPath = public_path($imageUrl);
            if (file_exists($fullPath)) {
                return asset($imageUrl);
            }
        }

        // FIXED: Check if placeholder exists before returning it
        $placeholderPath = 'images/product-placeholder.svg';
        if (file_exists(public_path($placeholderPath))) {
            return asset($placeholderPath);
        }

        // Ultimate fallback - return a data URL for a simple placeholder
        return 'data:image/svg+xml;base64,' . base64_encode('<svg xmlns="http://www.w3.org/2000/svg" width="300" height="300" viewBox="0 0 300 300"><rect width="300" height="300" fill="#f3f4f6"/><text x="150" y="150" text-anchor="middle" dy=".3em" fill="#9ca3af" font-family="Arial, sans-serif" font-size="14">No Image</text></svg>');
    }

    /**
     * Get the product's thumbnail URL.
     *
     * @return string
     */
    public function getThumbUrlAttribute(): string
    {
        $media = $this->getFirstMedia('product_images');
        if ($media) {
            return $media->getUrl('thumb');
        }

        return asset('images/product-placeholder.svg');
    }

    /**
     * Get all product gallery images.
     *
     * @return \Illuminate\Support\Collection
     */
    public function getGalleryImagesAttribute()
    {
        $images = collect();

        try {
            // Add main image first
            $mainImage = $this->getFirstMedia('product_images');
            if ($mainImage) {
                $images->push([
                    'url' => $mainImage->getUrl(),
                    'thumb' => $this->getMediaThumbUrl($mainImage),
                    'alt' => $this->name
                ]);
            }

            // Add gallery images
            $galleryImages = $this->getMedia('product_gallery');
            foreach ($galleryImages as $media) {
                $images->push([
                    'url' => $media->getUrl(),
                    'thumb' => $this->getMediaThumbUrl($media),
                    'alt' => $this->name
                ]);
            }
        } catch (\Exception $e) {
            \Log::error('Product gallery images error', [
                'product_id' => $this->id,
                'error' => $e->getMessage()
            ]);
        }

        // If no images, add placeholder
        if ($images->isEmpty()) {
            $images->push([
                'url' => asset('images/product-placeholder.svg'),
                'thumb' => asset('images/product-placeholder.svg'),
                'alt' => $this->name
            ]);
        }

        return $images;
    }

    /**
     * Get media thumbnail URL with fallback
     */
    private function getMediaThumbUrl($media)
    {
        try {
            // Try to get the thumb conversion
            if ($media->hasGeneratedConversion('thumb')) {
                return $media->getUrl('thumb');
            }

            // Fallback to original image
            return $media->getUrl();
        } catch (\Exception $e) {
            \Log::warning('Media thumb URL error', [
                'media_id' => $media->id,
                'error' => $e->getMessage()
            ]);

            // Fallback to original image
            return $media->getUrl();
        }
    }

    // Consider how 'stock_quantity' on the Product model itself should behave.
    // It could be the sum of all variant stocks, or the stock of a 'default' non-variant product.
    // For now, we'll assume it's separate or managed manually if variants are not used.



    // Similarly, the price displayed might need adjustment if variants exist.
    // This could be the price of the cheapest variant, or the base price.
    // For now, the 'price' attribute on Product is the base price.
    // The ProductVariant model has a 'getPriceAttribute' for its final price.

    public function isWishlisted()
    {
        if (!auth()->check()) {
            return false;
        }

        return auth()->user()->wishlist()->where('product_id', $this->id)->exists();
    }

    /**
     * Safely set featured status (admin only)
     */
    public function setFeatured(bool $featured): void
    {
        // This method should only be called after proper authorization
        $this->update(['is_featured' => $featured]);
    }

    /**
     * Safely set best seller status (admin only)
     */
    public function setBestSeller(bool $bestSeller): void
    {
        // This method should only be called after proper authorization
        $this->update(['is_best_seller' => $bestSeller]);
    }

    /**
     * Check if product has sufficient stock
     * Uses 'stock' field as the primary inventory field
     */
    public function hasSufficientStock(int $requestedQuantity): bool
    {
        // Use stock field as primary, fallback to quantity for backward compatibility
        $availableStock = $this->stock ?? $this->quantity ?? 0;
        return $availableStock >= $requestedQuantity;
    }

    /**
     * Get current stock level
     */
    public function getStockLevel(): int
    {
        // Use stock field as primary, fallback to quantity for backward compatibility
        return $this->stock ?? $this->quantity ?? 0;
    }

    /**
     * RACE CONDITION FIX: Atomically reduce stock with database-level locking
     * Uses 'stock' field as the primary inventory field
     */
    public function reduceStock(int $quantity): bool
    {
        if ($quantity <= 0) {
            throw new \InvalidArgumentException('Quantity must be positive');
        }

        // RACE CONDITION FIX: Use database transaction with row locking
        return \DB::transaction(function () use ($quantity) {
            // Lock the row for update to prevent race conditions
            $product = static::where('id', $this->id)->lockForUpdate()->first();

            if (!$product) {
                return false;
            }

            $currentStock = $product->stock ?? $product->quantity ?? 0;

            if ($currentStock < $quantity) {
                return false;
            }

            // Update stock atomically
            if ($product->stock !== null) {
                $affected = static::where('id', $this->id)
                    ->where('stock', '>=', $quantity)
                    ->update(['stock' => \DB::raw('stock - ' . $quantity)]);
            } else {
                $affected = static::where('id', $this->id)
                    ->where('quantity', '>=', $quantity)
                    ->update(['quantity' => \DB::raw('quantity - ' . $quantity)]);
            }

            // SECURITY FIX: Log stock changes for audit trail
            \Log::info('Stock reduced', [
                'product_id' => $this->id,
                'product_name' => $this->name,
                'quantity_reduced' => $quantity,
                'previous_stock' => $currentStock,
                'new_stock' => $currentStock - $quantity,
                'user_id' => auth()->id()
            ]);

            return $affected > 0;
        });
    }

    /**
     * RACE CONDITION FIX: Atomically increase stock (for returns/restocking)
     */
    public function increaseStock(int $quantity): bool
    {
        if ($quantity <= 0) {
            throw new \InvalidArgumentException('Quantity must be positive');
        }

        return \DB::transaction(function () use ($quantity) {
            // Lock the row for update
            $product = static::where('id', $this->id)->lockForUpdate()->first();

            if (!$product) {
                return false;
            }

            // Update stock atomically
            if ($product->stock !== null) {
                $affected = static::where('id', $this->id)
                    ->update(['stock' => \DB::raw('stock + ' . $quantity)]);
            } else {
                $affected = static::where('id', $this->id)
                    ->update(['quantity' => \DB::raw('quantity + ' . $quantity)]);
            }

            // SECURITY FIX: Log stock changes for audit trail
            \Log::info('Stock increased', [
                'product_id' => $this->id,
                'product_name' => $this->name,
                'quantity_added' => $quantity,
                'user_id' => auth()->id()
            ]);

            return $affected > 0;
        });
    }

    /**
     * RACE CONDITION FIX: Reserve stock for pending orders
     */
    public function reserveStock(int $quantity, string $reservationId): bool
    {
        if ($quantity <= 0) {
            throw new \InvalidArgumentException('Quantity must be positive');
        }

        return \DB::transaction(function () use ($quantity, $reservationId) {
            // Lock the row for update
            $product = static::where('id', $this->id)->lockForUpdate()->first();

            if (!$product) {
                return false;
            }

            $currentStock = $product->stock ?? $product->quantity ?? 0;

            if ($currentStock < $quantity) {
                return false;
            }

            // Create stock reservation record
            \DB::table('stock_reservations')->insert([
                'product_id' => $this->id,
                'quantity' => $quantity,
                'reservation_id' => $reservationId,
                'expires_at' => now()->addMinutes(15), // 15-minute reservation
                'created_at' => now(),
                'updated_at' => now(),
            ]);

            return true;
        });
    }
}
